{"version": "V2.1R24C01B009", "name": "AILPHA_CDMS", "type": "A1000-32-2", "base_image": [{"name": "ailpha-base", "version": "v2.0", "build": {"image": "ailpha-base:v2.0"}}, {"name": "ailpha-base", "version": "v1.1", "build": {"image": "ailpha-base:v1.1"}}, {"name": "baasflink", "cardinality": 1, "version": "v4.9.112_beta_dev", "build": {"image": "baasflink:v4.9.112_beta_dev", "pkgurl": "http://**********/OUT/202409/ailpha-ext-baasflink-v4.9.112_beta_dev-14ff3dad-2402261021.zip", "dockerfile": "file://docker/Dockerfile"}}, {"name": "cdmsflink", "cardinality": 1, "version": "v2.1.1.0_release_Cdms", "build": {"image": "cdmsflink:v2.1.1.0_release_Cdms", "pkgurl": "http://**********/OUT/202405/ailpha-ext-cdmsflink-v2.1.1.0_release_Cdms-c2b0fb86-2402040821.zip", "dockerfile": "file://docker/Dockerfile"}}], "service": {"app": [], "common": [{"name": "baas", "cardinality": 1, "version": "v4.9.112_beta_dev", "build": {"image": "baas:v4.9.112_beta_dev", "pkgurl": "http://**********/OUT/202409/ailpha-ext-baas-v4.9.112_beta_dev-14ff3dad_6ab5e4c7_5.1.2.4-2402261014.zip", "dockerfile": "file://docker/Dockerfile", "composefile": "file://docker/docker-compose.yml"}}, {"name": "auth", "version": "V1.1R24C01B003", "cardinality": 1, "build": {"image": "auth:V1.1R24C01B003", "pkgurl": "http://**********/OUT/202408/DAS-AUTH-V1.1R24C01B003-X86-standard-auth-52a24078_455257c1-20240223014442.zip", "dockerfile": "file://docker/Dockerfile", "composefile": "file://docker/docker-compose.yml"}}, {"name": "cdms", "cardinality": 1, "version": "v2.1.1.0_release_Cdms", "build": {"image": "cdms:v2.1.1.0_release_Cdms", "pkgurl": "http://**********/OUT/202405/ailpha-ext-cdms-v2.1.1.0_release_Cdms-c2b0fb86_5a221be0-2402040816.zip", "dockerfile": "file://docker/Dockerfile", "composefile": "file://docker/docker-compose.yml"}}], "base": [{"name": "zookeeper", "cardinality": 3, "version": "3.8.3", "build": {"image": "zookeeper:3.8.3"}}, {"name": "datanode", "cardinality": 2, "version": "3.3.6-cn", "build": {"image": "hadoop:3.3.6-cn"}}, {"name": "namenode", "cardinality": 1, "version": "3.3.6-cn", "build": {"image": "hadoop:3.3.6-cn"}}, {"name": "snamenode", "cardinality": 1, "version": "3.3.6-cn", "build": {"image": "hadoop:3.3.6-cn"}}, {"name": "resourcemanager", "cardinality": 1, "version": "3.3.6-cn", "build": {"image": "hadoop:3.3.6-cn"}}, {"name": "resourcemanagerauth", "cardinality": 1, "version": "2.8.2", "build": {"image": "haproxy:2.8.2"}}, {"name": "nodemanager", "cardinality": 1, "version": "3.3.6-cn", "build": {"image": "hadoop:3.3.6-cn"}}, {"name": "kafka", "cardinality": 3, "version": "3.6.0", "build": {"image": "kafka:3.6.0"}}, {"name": "clickhouse", "cardinality": 1, "version": "*********-alpine", "build": {"image": "clickhouse-server:*********-alpine"}}, {"name": "elasticsearch", "cardinality": 1, "version": "7.17.14", "build": {"image": "elasticsearch:7.17.14"}}, {"name": "kibana", "cardinality": 1, "version": "7.17.13", "build": {"image": "kibana:7.17.13"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cardinality": 1, "version": "2.8.2", "build": {"image": "haproxy:2.8.2"}}, {"name": "postgresql", "cardinality": 1, "version": "14.8-alpine", "build": {"image": "postgres:14.8-alpine"}}, {"name": "nacos", "cardinality": 1, "version": "2.2.3-alpine", "build": {"image": "nacos-server:v2.2.3-ailpha"}}, {"name": "nginx", "cardinality": 1, "version": "1.23.4", "build": {"image": "nginx:1.23.4"}}, {"name": "redis", "cardinality": 1, "version": "5.0.8-alpine", "build": {"image": "redis:5.0.8-alpine"}}, {"name": "prometheus", "cardinality": 1, "version": "2.47.0", "build": {"image": "prometheus:2.47.0"}}, {"name": "grafana", "cardinality": 1, "version": "10.1.1", "build": {"image": "grafana:10.1.1"}}], "extend": []}, "configuration": {"init": [{"sname": "nginx"}, {"sname": "resources", "url": ["http://***********/luanbird/resources-20240124.tar.gz"]}], "ports": [{"sname": "auth", "type": "ClusterIp", "ClusterIp": "**************", "port": 9001}, {"sname": "nginx", "type": "NodePort", "sport": 443, "dport": 443}, {"sname": "baas", "type": "ClusterIp", "ClusterIp": "**************", "port": 8999}, {"sname": "cdms", "type": "ClusterIp", "ClusterIp": "**************", "port": 9001}]}}