{"baas": {"name": "baas", "image": "baas:v4.9.111_beta_dev", "number": 1, "memory": 20}, "auth": {"name": "auth", "image": "auth:V1.1R24C01B003", "number": 1, "memory": 4}, "cdms": {"name": "cdms", "image": "cdms:v2.1.1.0_release_Cdms", "number": 1, "memory": 20}, "zookeeper": {"name": "zookeeper", "number": 1, "memory": 2, "limit": 3, "image": "zookeeper:3.8.3"}, "datanode": {"name": "datanode", "number": 1, "memory": 26, "image": "hadoop:3.3.6-cn"}, "namenode": {"name": "namenode", "number": 1, "memory": 6, "image": "hadoop:3.3.6-cn"}, "snamenode": {"name": "snamenode", "number": 1, "memory": 8, "image": "hadoop:3.3.6-cn"}, "resourcemanager": {"name": "resourcemanager", "number": 1, "memory": 6, "image": "hadoop:3.3.6-cn"}, "resourcemanagerauth": {"name": "resourcemanagerauth", "number": 1, "memory": 1, "image": "haproxy:2.8.2"}, "nodemanager": {"name": "nodemanager", "number": 1, "memory": 26, "image": "hadoop:3.3.6-cn"}, "kafka": {"name": "kafka", "number": 1, "memory": 4, "limit": 12, "image": "kafka:3.6.0"}, "clickhouse": {"name": "clickhouse", "number": 1, "memory": 10, "image": "clickhouse-server:22.8.9.24-alpine"}, "elasticsearch": {"name": "elasticsearch", "number": 1, "memory": 8, "image": "elasticsearch:7.17.14"}, "kibana": {"name": "kibana", "number": 1, "memory": 1, "image": "kibana:7.17.13"}, "kibanaauth": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "number": 1, "memory": 1, "image": "haproxy:2.8.2"}, "postgresql": {"name": "postgresql", "number": 1, "memory": 4, "image": "postgres:14.8-alpine"}, "nacos": {"name": "nacos", "number": 1, "memory": 2, "image": "nacos-server:v2.2.3-ailpha"}, "nginx": {"name": "nginx", "number": 1, "memory": 2, "image": "nginx:1.23.4"}, "redis": {"name": "redis", "number": 1, "memory": 10, "image": "redis:5.0.8-alpine"}, "prometheus": {"name": "prometheus", "number": 1, "memory": 2, "image": "prometheus:2.47.0"}, "grafana": {"name": "grafana", "number": 1, "memory": 2, "image": "grafana:10.1.1"}}