version: "3"
services:
  baas:
    image: ailpha-registry:5000/baas:v4.9.63_beta_dev
    container_name: 1-baas-1
    #加入环境变量文件 .env
    env_file:
      - midware.env

    environment:

      - ES_SERVERS=http://1-elasticsearch-1:9200,http://1-elasticsearch-2:9200,http://1-elasticsearch-3:9200,http://1-elasticsearch-4:9200

    labels:
      - "cn=1-baas-1"
      - "sn=baas"
    deploy:
      restart_policy:
        condition: unless-stopped
      resources:
        limits:
          memory:  20G
        reservations:
          memory: 100M

    healthcheck:
      test: curl http://localhost:8999/ || exit 1
      interval: 30s
      timeout: 5s
      retries: 20

    hostname: 1-baas-1

    volumes:

      - /data/1-baas-1/logs:/usr/hdp/*******-37/baas/baasweb/logs

      - /data/1-baas-1/data:/data

      - /opt/ailpha-install/script/resources:/usr/hdp/*******-37/baas/baasweb/resources

      - /opt/ailpha-install/license:/share_data/lic

      - /etc/localtime:/etc/localtime:ro

      - /opt/ailpha-install/cluster_init/resolv.conf:/etc/resolv.conf

    networks:
      - cilium-net
networks:
  cilium-net:
    external: true