version: "3"
services:
  cdms:
    image: ailpha-registry:5000/cdms:v2.2.28_beta_dev
    container_name: 1-cdms-1
    #加入环境变量文件 .env
    env_file:
      - midware.env

    environment:

      - ES_SERVERS=http://1-elasticsearch-1:9200,http://1-elasticsearch-2:9200,http://1-elasticsearch-3:9200,http://1-elasticsearch-4:9200

    labels:
      - "cn=1-cdms-1"
      - "sn=cdms"
    deploy:
      restart_policy:
        condition: unless-stopped
      resources:
        limits:
          memory:  20G
        reservations:
          memory: 100M

    healthcheck:
      test: curl http://localhost:9001/ || exit 1
      interval: 30s
      timeout: 5s
      retries: 20

    hostname: 1-cdms-1

    volumes:

      - /data/1-cdms-1/logs:/usr/hdp/*******-37/cdms/cdmsweb/logs

      - /data/1-cdms-1/data:/data

      - /opt/ailpha-install/license:/share_data/lic

      - /etc/localtime:/etc/localtime:ro

      - /opt/ailpha-install/cluster_init/resolv.conf:/etc/resolv.conf

    networks:
      - cilium-net
networks:
  cilium-net:
    external: true