version: "3"
services:
  auth:
    image: ailpha-registry:5000/das-auth:v2.2.28_beta_dev
    container_name: 1-auth-1
    #加入环境变量文件 .env
    env_file:
      - midware.env

    labels:
      - "cn=1-auth-1"
      - "sn=auth"
    deploy:
      restart_policy:
        condition: unless-stopped
      resources:
        limits:
          memory:  4G
        reservations:
          memory: 100M

    healthcheck:
      test: curl  http://localhost:9001/ || exit 1
      interval: 30s
      timeout: 5s
      retries: 5

    hostname: 1-auth-1

    volumes:

      - /data/1-auth-1/logs:/usr/hdp/*******-37/bigdata/ailpha-auth/logs

      - /data/1-auth-1/data:/data

      - /etc/localtime:/etc/localtime:ro

    networks:
      - cilium-net
networks:
  cilium-net:
    external: true